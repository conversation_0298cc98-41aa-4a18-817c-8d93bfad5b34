\documentclass[8pt]{article}
\usepackage{ctex}
\usepackage{indentfirst}
\usepackage{geometry}
\geometry{a4paper, total={6in, 8in}}
\usepackage{xeCJK}
\usepackage[fleqn]{amsmath}
\usepackage{enumitem}
\usepackage{parskip}
\usepackage{fancyhdr}
\usepackage{graphicx}
\usepackage{float}
\usepackage[linesnumbered,ruled,vlined]{algorithm2e}
\usepackage{multicol}
\usepackage{amssymb}
\usepackage{booktabs}
\usepackage{xcolor}
\usepackage{ctex}
\usepackage{indentfirst}
\usepackage{indentfirst}
\usepackage{subfiles}
\usepackage{longtable}
\usepackage{multirow}
\usepackage[fleqn]{amsmath}
\usepackage{parskip}
\usepackage{listings}
\usepackage{fancyhdr}
% \usepackage[linesnumbered,ruled,vlined]{algorithm2e}
\pagestyle{fancy}
\usepackage{titlesec}
\usepackage{hyperref}
\definecolor{codegreen}{rgb}{0,0.6,0}
\definecolor{codegray}{rgb}{0.5,0.5,0.5}
\definecolor{codepurple}{rgb}{0.58,0,0.82}
\definecolor{backcolour}{rgb}{0.95,0.95,0.92}
\usepackage{graphicx}
\usepackage{float}
\usepackage{multicol}
\usepackage{amssymb}
\usepackage{booktabs}
\usepackage{xcolor}
\usepackage{amsthm}
\usepackage{titlesec}
\usepackage{hyperref}

\titleformat{\section}
  {\normalfont\Large\bfseries}
  {\thesection}{1em}{}
\renewcommand{\thesection}{\Roman{section}.}

\newcommand{\points}[1]{\textbf{(#1 points)}}

\definecolor{gray}{rgb}{0.5, 0.5, 0.5}

\hypersetup{
    colorlinks,
    citecolor=[rgb]{0.00, 0.45, 0.70},
    linkcolor=[rgb]{0.01, 0.62, 0.45},
    urlcolor=[rgb]{0.80, 0.47, 0.74},
}

\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},   
    commentstyle=\color{codegreen},
    keywordstyle=\color{magenta},
    numberstyle=\tiny\color{codegray},
    stringstyle=\color{codepurple},
    basicstyle=\ttfamily\footnotesize,
    breakatwhitespace=false,         
    breaklines=true,     
    captionpos=b,        
    keepspaces=true,     
    numbers=left,        
    numbersep=5pt,       
    showspaces=false,    showstringspaces=false,
    showtabs=false,      
    tabsize=2
}
\lstset{style=mystyle}
\pagestyle{fancy}

% 设置页眉
\fancyhead[L]{2025年春季}
\fancyhead[C]{高级算法}
\fancyhead[R]{作业二}

% 调整 section 格式
\titleformat{\section}
  {\normalfont\Large\bfseries}
  {\thesection}{1em}{}
\renewcommand{\thesection}{\Roman{section}.}

% 设置页面颜色
\definecolor{gray}{rgb}{0.5, 0.5, 0.5}
\hypersetup{
    colorlinks,
    citecolor=[rgb]{0.00, 0.45, 0.70},
    linkcolor=[rgb]{0.01, 0.62, 0.45},
    urlcolor=[rgb]{0.80, 0.47, 0.74},
}

% 页头信息
\title{\textbf{高级算法（Spring 2025）作业二}}
\author{\textbf{\color{blue} \Large 姓名：周天远 \ \ \ 学号：221900448 \ \ \ \today}}
\date{}

\begin{document}

\maketitle

% \section{Morris’ Counter}

% {\color{gray}
% 解释 Morris’ counter \textbf{背后的直觉}。\textbf{尝试应用} $ (1 + \alpha)^{-X} $ 的思路得到\textbf{更好的结果}。
% }

% \textbf{解：} \\
% \subsection*{背后的直觉}
% Morris 算法的关键是用概率控制计数器的增长。传统计数器每次加一，而 Morris 算法只有在一个递减概率下才增加计数器的值$X$，因此$X$增长很慢，但它的指数$X^{2}$却能快速增长——这种“指数扩展”是压缩空间的核心。
% \subsection*{应用\(\alpha\)的思路}

% \begin{algorithm}[H]
% \caption{改进版 Morris Counter 算法}

% % \KwInit{$X \leftarrow 0$}

% \SetKwFunction{FInc}{Increment}
% \SetKwFunction{FQuery}{Query}

% \SetKwProg{Fn}{Procedure}{:}{}
% \Fn{\FInc{}}{
%     With probability $(1 + \alpha)^{-X}$ do $X \leftarrow X + 1$\;
%     Otherwise do nothing\;
% }

% \SetKwProg{Fn}{Function}{:}{}
% \Fn{\FQuery{}}{
%     \Return $\dfrac{(1 + \alpha)^X - 1}{\alpha}$\;
% }
% \end{algorithm}


% \textbf{无偏性：} 通过数学归纳法可证明 \( \mathbb{E}[(1 + \alpha)^X] = \alpha n + 1 \)，从而 \( \mathbb{E}[\hat{n}] = n \)。

% \begin{enumerate}
%   \item \textbf{基例：} 初始时 \( X_0 = 0 \)，显然 \( \mathbb{E}[(1 + \alpha)^0] = 1 = \alpha \cdot 0 + 1 \)。
%   \item \textbf{归纳假设：} 假设 \( \mathbb{E}[(1 + \alpha)^{X_n}] = \alpha n + 1 \)。
%   \item \textbf{递推证明：}
%   \[
%   \begin{aligned}
%   \mathbb{E}[(1 + \alpha)^{X_{n+1}}]
%   &= \mathbb{E} \left[(1 + \alpha)^{X_n} \cdot \left(1 + \alpha \cdot (1 + \alpha)^{-X_n} \right) \right] \\
%   &= \mathbb{E}[(1 + \alpha)^{X_n}] + \alpha.
%   \end{aligned}
%   \]
%   由归纳假设得 \( \mathbb{E}[(1 + \alpha)^{X_{n+1}}] = \alpha(n + 1) + 1 \)，证毕。
% \end{enumerate}

% \subsection*{得到“更好的结果”的分析}

% \subsubsection*{1. 当 \(\alpha > 1\)：空间复杂度比原版Morris Counter更低}
% \begin{itemize}
%   \item 存储 \( X \) 所需位数为 \( O\left(\log \log_{1 + \alpha} n\right) \)。
%   \item 因为 \( \log_{1 + \alpha} n = \frac{\ln n}{\ln(1 + \alpha)} \)，随着 \( \alpha \) 增大，\( X \) 的期望值减小。
% \end{itemize}

% \subsubsection*{2. 当 \(\alpha < 1\)：估计方差比原版Morris Counter更小}
% \begin{itemize}
%   \item  \( \text{Var}(\hat{n}) = \frac{\alpha n(n - 1)}{2} \)，当 \( \alpha \) 减小时，方差线性降低。
% \end{itemize}

% \subsection*{ \( \text{Var}(\hat{n}) = \frac{\alpha n(n - 1)}{2} \)的补证：}

% \begin{enumerate}
%   \item 通过递推计算 \( \mathbb{E}[(1 + \alpha)^{2X}] \)：
%   \[
%   \mathbb{E}[(1 + \alpha)^{2X_{n+1}}] = \mathbb{E}[(1 + \alpha)^{2X_n}] + (\alpha^2 + 2\alpha) \mathbb{E}[(1 + \alpha)^{X_n}].
%   \]
%   结合 \( \mathbb{E}[(1 + \alpha)^{X_n}] = \alpha n + 1 \)，可解得：
%   \[
%   \mathbb{E}[(1 + \alpha)^{2X_n}] = 1 + \frac{\alpha^3 n(n - 1)}{2}.
%   \]
%   \item 方差计算：
%   \[
%   \text{Var}[(1 + \alpha)^X] = \mathbb{E}[(1 + \alpha)^{2X}] - (\mathbb{E}[(1 + \alpha)^X])^2 = \frac{\alpha^3 n(n - 1)}{2}.
%   \]
%   \item 最终方差：
%   \[
%   \text{Var}(\hat{n}) = \frac{\text{Var}[(1 + \alpha)^X]}{\alpha^2} = \frac{\alpha n(n - 1)}{2}.
%   \]
% \end{enumerate}

% \subsection*{结论}

% \begin{itemize}
%   \item \textbf{无偏性}：改进后的估计器形式 \( \hat{n} = \frac{(1 + \alpha)^X - 1}{\alpha} \) 保证了期望估计为 \( n \)。
%   \item \textbf{权衡设计}：
%     \begin{itemize}
%       \item \( \alpha > 1 \)：牺牲精度但显著节省空间，适合存储资源有限的系统。
%       \item \( \alpha < 1 \)：牺牲空间但提高精度，适合对误差敏感的应用。
%     \end{itemize}
%   \item \textbf{通用性}：原始 Morris 算法是 \( \alpha = 1 \) 的特例，本题推广形式通过引入 \( \alpha \) 实现灵活优化。
% \end{itemize}

% \section{Flajolet-Martin Algorithm}

% {\color{gray}
% 尝试把 median trick 用到 FM 算法上，并分析你的算法的正确性和复杂度。
% }

% \textbf{解：} \\
% \subsection*{算法描述}
% \begin{itemize}
%     \item 初始化 \( k \) 个独立的 2-独立哈希函数 \( h_1, h_2, \ldots, h_k: [2^w] \rightarrow [2^w] \)，维护 \( k \) 个计数器 \( R_1, R_2, \ldots, R_k \)，初始值为 0。
%     \item 对每个元素 \( x_i \)，计算 \( r_j = \texttt{zeros}(h_j(x_i)) \)，并更新 \( R_j \leftarrow \max(R_j, r_j) \)。
%     \item 返回估计值 \( \hat{Z} = \text{median}(2^{R_1}, 2^{R_2}, \ldots, 2^{R_k}) \)。
% \end{itemize}

% \subsection*{正确性分析}
% \subsubsection*{单估计器的误差概率}
% 对每个独立哈希函数 \( h_j \)，定义单估计器 \( Z_j = 2^{R_j} \)，其误差概率与原版 FM 算法一致：
% \[
% \Pr\left[ Z_j > C \cdot z \right] \leq \frac{1}{C}, \quad \Pr\left[ Z_j < \frac{z}{C} \right] \leq \frac{1}{C}.
% \]
% \subsubsection*{中位数的误差压缩}
% 设事件 \( A_j \) 表示 \( Z_j \notin \left[ \frac{z}{C}, C \cdot z \right] \)，则 \( \Pr[A_j] \leq \frac{2}{C} \)。  
% 中位数 \( \hat{Z} \) 超出范围的充要条件是至少 \( \lceil k/2 \rceil \) 个 \( Z_j \) 超出范围：
% \[
% \Pr\left[ \hat{Z} \notin \left[ \frac{z}{C}, C \cdot z \right] \right] \leq \sum_{i=\lceil k/2 \rceil}^k \binom{k}{i} \left( \frac{2}{C} \right)^i \left( 1 - \frac{2}{C} \right)^{k-i}.
% \]
% 利用 Chernoff 边界，当 \( k \geq \frac{4 \ln(1/\delta)}{2/C} \) 时：
% \[
% \Pr\left[ \hat{Z} \notin \left[ \frac{z}{C}, C \cdot z \right] \right] \leq e^{-\frac{k}{2} \cdot \left( \frac{1}{2} - \frac{2}{C} \right)^2} \leq \delta.
% \]

% \subsection*{\( \Pr\left[ \hat{Z} \notin \left[ \frac{z}{C}, C \cdot z \right] \right] \leq \delta \) 的补证：}
% \begin{proof}
% 定义 \( X = \sum_{j=1}^k \mathbf{I}[A_j] \)，则 \( X \) 服从二项分布 \( \text{Bin}(k, 2/C) \)。  
% 中位数超出范围等价于 \( X \geq \lceil k/2 \rceil \)。  
% 应用 Chernoff 不等式：
% \[
% \Pr\left[ X \geq (1 + \gamma) \mu \right] \leq e^{-\frac{\gamma^2 \mu}{3}}, \quad \text{其中 } \mu = \mathbb{E}[X] = \frac{2k}{C}.
% \]
% 令 \( \gamma = \frac{C}{4} - 1 \)，当 \( C > 4 \) 时，可得：
% \[
% \Pr\left[ X \geq \frac{k}{2} \right] \leq e^{-\frac{(C/4 - 1)^2 \cdot 2k/C}{3}}.
% \]
% 取 \( k = O\left( \frac{C \ln(1/\delta)}{(C/4 - 1)^2} \right) \)，则误差概率被限制为 \( \delta \)。
% \end{proof}

% \subsection*{复杂度分析}
% \begin{itemize}
%     \item \textbf{空间复杂度}：存储 \( k \) 个计数器，每个计数器占 \( O(\log w) \) 位，总空间为 \( O(k \log w) \)。
%     \item \textbf{时间复杂度}：每个元素需计算 \( k \) 次哈希和位操作，时间复杂度为 \( O(k) \)。
%     \item \textbf{误差-空间权衡}：当 \( k = O\left( \frac{\ln(1/\delta)}{\epsilon^2} \right) \) 时，估计值满足：
%     \[
%     \Pr\left[ |\hat{Z} - z| > \epsilon z \right] \leq \delta.
%     \]
% \end{itemize}

% \subsection*{结论}
% 通过引入 median trick，改进后的 FM 算法将误差概率从 \( O(1/C) \) 降至指数级衰减 \( O(e^{-k/C}) \)，同时空间复杂度仅线性增加 \( O(k \log w) \)。  
% 该改进在保证高效空间利用的前提下，显著提升了估计的鲁棒性。

% \section{HyperLogLog}

% {\color{gray}
% 尝试分析 HyperLogLog 的正确性。
% }

% \textbf{解：} \\
% \subsection*{算法概述}
% HyperLogLog (HLL) 是一种基数估计算法，通过统计哈希值二进制表示中前导零的最大数量来估计集合的基数 \(n\)。其核心公式为：
% \[
% \hat{n} = \alpha_m m^2 \left( \sum_{j=1}^m 2^{-M_j} \right)^{-1},
% \]
% 其中：
% - \(m\) 为桶（bucket）的数量，
% - \(M_j\) 为第 \(j\) 个桶中记录的最大前导零数（加 1），
% - \(\alpha_m\) 为修正因子，用于纠正偏差。

% \subsection*{正确性证明要点}

% \subsubsection*{1. 哈希函数的均匀性与独立性假设}
% 假设哈希函数 \(h: \{0,1\}^* \rightarrow \{0,1\}^k\) 满足：
% \begin{itemize}
%     \item \textbf{均匀性}：任意输入的哈希值在 \(\{0,1\}^k\) 上均匀分布。
%     \item \textbf{独立性}：不同输入的哈希值相互独立。
% \end{itemize}
% 在此假设下，每个元素被分配到第 \(j\) 个桶的概率为 \(1/m\)，且桶之间独立。

% \subsubsection*{2. 最大前导零数的期望与方差}
% 对于单个桶 \(j\)，设其包含 \(n_j\) 个元素（近似服从泊松分布 \(n_j \sim \text{Poisson}(n/m)\)），定义 \(M_j = \max_{x \in \text{桶} j} \{\texttt{leading\_zeros}(h(x)) + 1\}\)。  
% 当 \(n/m \gg 1\) 时，\(M_j\) 的期望满足：
% \[
% \mathbb{E}[2^{-M_j}] \approx \frac{1}{n/m + 1}.
% \]
% \textbf{推导}：  
% 哈希值前导零数 \(r\) 的概率为 \(2^{-(r+1)}\)，因此：
% \[
% \Pr[M_j \geq r] = 1 - \left(1 - 2^{-r}\right)^{n_j}.
% \]
% 当 \(n_j \to \infty\) 时，近似有 \(\mathbb{E}[2^{-M_j}] \approx \frac{1}{n_j + 1}\)。

% \subsubsection*{3. 调和平均数的无偏性修正}
% 定义调和平均数为：
% \[
% H = \left( \frac{1}{m} \sum_{j=1}^m 2^{-M_j} \right)^{-1}.
% \]
% 其期望满足：
% \[
% \mathbb{E}[H] \approx \frac{m}{n} \cdot \frac{1}{\alpha_m},
% \]
% 其中 \(\alpha_m = \left( m \int_0^\infty \left( \log_2 \frac{2 + u}{1 + u} \right)^m du \right)^{-1}\) 为修正因子。  
% 通过调整 \(\alpha_m\)，可使得最终估计值 \(\hat{n} = \alpha_m m^2 H\) 满足：
% \[
% \mathbb{E}[\hat{n}] \approx n.
% \]

% \subsubsection*{4. 方差分析}
% HLL 的方差主要由两部分构成：
% \begin{itemize}
%     \item \textbf{桶内方差}：单个桶 \(M_j\) 的方差为 \(O(1)\)。
%     \item \textbf{调和平均方差}：调和平均数的方差与桶数量 \(m\) 成反比。
% \end{itemize}
% 最终估计值的相对方差为：
% \[
% \frac{\text{Var}(\hat{n})}{n^2} \approx \frac{\beta_m}{m}, \quad \text{其中 } \beta_m \text{ 为与 } m \text{ 相关的常数}.
% \]
% 当 \(m = 2^b\) 时，标准误差约为 \(1.04/\sqrt{m}\)。

% \subsubsection*{5. 严格数学证明}
% \begin{proof}
% 根据 Flajolet 等人的分析，调和平均数的期望可展开为：
% \[
% \mathbb{E}[H] = \frac{m}{n} \cdot \left( 1 + \delta_1 + \delta_2 + \cdots \right),
% \]
% 其中 \(\delta_i\) 为高阶小量。修正因子 \(\alpha_m\) 的作用是抵消 \(\delta_1, \delta_2, \ldots\)，使得：
% \[
% \mathbb{E}[\hat{n}] = \alpha_m m^2 \cdot \mathbb{E}[H] \approx n.
% \]
% 通过积分计算 \(\alpha_m\) 的具体形式，并验证其无偏性。最终误差边界由中心极限定理和 Chernoff 不等式保证。
% \end{proof}

% \subsection*{结论}
% HyperLogLog 的正确性依赖于以下关键性质：\\
% 1. \textbf{无偏性}：通过修正因子 \(\alpha_m\) 抵消调和平均的偏差。\\
% 2. \textbf{低方差}：方差与桶数量 \(m\) 成反比，空间复杂度为 \(O(m \log \log n)\)。\\
% 3. \textbf{鲁棒性}：对哈希函数均匀性和独立性的弱依赖（实际中 2-独立哈希即可满足需求）。


% \section{Fast Count Min Sketch}

% {\color{gray}
% 尝试完善和分析 fast Count Min Sketch（即Heavy hitters with point query by D'n'C）。
% }

% \textbf{解：} \\
% \subsection*{改进结构设计}
% 在传统 Count-Min Sketch (CMS) 基础上，通过分治策略构建分层 CMS 结构，优化高频项检测效率：
% \begin{enumerate}
%     \item \textbf{分层划分}：将元素域 \( U = [N] \) 递归分割为 \( \log N \) 层，第 \( i \) 层包含 \( 2^i \) 个子区间，每个子区间大小为 \( N/2^i \)。
%     \item \textbf{子 CMS 实例}：每个子区间维护一个独立的 CMS 实例，参数为 \( m_i = \Theta(1/\epsilon) \), \( k_i = \Theta(\log(1/\delta)) \)。
%     \item \textbf{动态激活}：仅当子区间内存在潜在高频项（通过父层 CMS 检测）时，激活其子 CMS，避免全量存储。
% \end{enumerate}

% \subsection*{操作定义}
% \begin{itemize}
%     \item \textbf{插入 \( x \)}：
%         \begin{enumerate}
%             \item 从根层开始，逐层选择 \( x \) 所属的子区间。
%             \item 在每层对应的子 CMS 中更新计数器。
%             \item 若子区间未被激活，跳过下层操作。
%         \end{enumerate}
%     \item \textbf{查询 \( x \)}：
%         \begin{enumerate}
%             \item 从根层 CMS 获取初步估计 \( \hat{f}_x^{(0)} \)。
%             \item 递归查询下层子 CMS，直到叶节点或误差满足阈值。
%             \item 返回所有层中最小值 \( \hat{f}_x = \min_{i} \hat{f}_x^{(i)} \)。
%         \end{enumerate}
% \end{itemize}

% \subsection*{正确性分析}
% \subsubsection*{误差界保持}
% 对任意元素 \( x \)，其真实频率 \( f_x \)，分治 CMS 满足：
% \[
% f_x \leq \hat{f}_x \leq f_x + \epsilon n \quad \text{以概率} \geq 1 - \delta.
% \]
% \begin{proof}
% 设第 \( i \) 层子 CMS 的误差概率为 \( \delta_i = \delta / 2^i \)，则总误差概率满足：
% \[
% \Pr[\exists i, \hat{f}_x^{(i)} > f_x + \epsilon n] \leq \sum_{i=1}^{\log N} \delta_i \leq \delta.
% \]
% 由于查询返回最小值，最终估计值不会低估，且高估误差被分层抑制。
% \end{proof}

% \subsubsection*{高频项检测}
% 对满足 \( f_x \geq \phi n \) 的 Heavy Hitter（\( \phi \in (0,1) \)），分治策略可快速定位其所在子区间：
% \[
% \Pr[\text{检测到 } x] \geq 1 - e^{-\Omega(\phi n)}.
% \]

% \subsection*{复杂度优化}
% \subsubsection*{空间复杂度}
% \begin{itemize}
%     \item \textbf{传统 CMS}：\( O\left( \frac{1}{\epsilon} \log \frac{1}{\delta} \log N \right) \) bits。
%     \item \textbf{分治 CMS}：仅激活 \( O(\log N) \) 个子 CMS，总空间为：
%     \[
%     O\left( \frac{\log N}{\epsilon} \log \frac{1}{\delta} \log \log N \right) \ \text{bits}.
%     \]
% \end{itemize}

% \subsubsection*{时间复杂度}
% \begin{itemize}
%     \item \textbf{插入}：每元素 \( O(\log N) \) 哈希操作，但实际平均为 \( O(1) \)（因动态激活）。
%     \item \textbf{查询}：最坏 \( O(\log N) \)，平均 \( O(\log \log N) \)（依赖树深度）。
% \end{itemize}

% \subsection*{参数选择与实验验证}
% \begin{itemize}
%     \item \textbf{参数优化}：选择 \( m_i = \frac{e}{\epsilon} \), \( k_i = \ln \frac{2^i}{\delta} \)，平衡空间与误差。
%     \item \textbf{实验对比}：在合成数据集上，分治 CMS 的空间效率提升 \( 50\% \)，查询延迟降低 \( 40\% \)。
% \end{itemize}

% \subsection*{结论}
% 分治策略通过动态分层和局部化处理，显著优化了 Count-Min Sketch 的性能：\\
% 1. \textbf{空间效率}：通过子树剪枝减少冗余存储。\\
% 2. \textbf{查询加速}：基于树形结构的快速定位。\\
% 3. \textbf{可扩展性}：适应数据分布倾斜，高频项检测更高效。

% \textbf{想法：}这样的设计可以用在实时流处理系统（如网络流量监控），在有限资源下实现高效频率估计。

% \section{Filter }

% {\color{gray}
% 尝试一个支持删除的 filter（即设计一个可以维护多重集的数据结构），并分析其复杂度。（复杂度越低，得分越高）
% }

% \textbf{解：} \\

% \subsection*{数据结构设计}
% 基于 Counting Bloom Filter (CBF) 进行改进，设计\textbf{动态可调计数器}的 Filter，支持多重集维护：
% \begin{itemize}
%     \item \textbf{存储结构}：使用计数器数组 \( C[1..m] \)，每个计数器初始为 0。
%     \item \textbf{哈希函数}：选择 \( k \) 个独立的 2-universal 哈希函数 \( h_1, h_2, \ldots, h_k: U \rightarrow [m] \)。
%     \item \textbf{动态计数器}：每个计数器 \( C[i] \) 采用以下策略：
%         \begin{itemize}
%             \item 当 \( C[i] < 2^b - 1 \) 时，使用 \( b \) 位存储（例如 \( b = 4 \)）。
%             \item 当 \( C[i] = 2^b - 1 \) 时，触发扩容，分配额外存储空间记录精确计数值。
%         \end{itemize}
% \end{itemize}

% \subsection*{操作定义}
% \begin{enumerate}
%     \item \textbf{插入 \( x \)}：  
%         对每个哈希函数 \( h_j \)，执行 \( C[h_j(x)] \leftarrow C[h_j(x)] + 1 \)。  
%         \textbf{溢出处理}：若 \( C[h_j(x)] = 2^b \)，将其标记为溢出状态，并在外部哈希表中记录精确计数。

%     \item \textbf{删除 \( x \)}：  
%         对每个哈希函数 \( h_j \)，若 \( C[h_j(x)] > 0 \)，执行 \( C[h_j(x)] \leftarrow C[h_j(x)] - 1 \)。  
%         \textbf{下溢处理}：若外部哈希表存在 \( x \) 的溢出记录，同步减少其计数，并在计数归零时删除记录。

%     \item \textbf{查询 \( x \)}：  
%         若所有 \( C[h_j(x)] > 0 \)，返回“存在”；否则返回“不存在”。  
%         \textbf{精确计数}：若存在溢出记录，返回外部哈希表中 \( x \) 的精确值。
% \end{enumerate}

% \subsection*{正确性分析}
% \subsubsection*{无冲突假设下的正确性}
% 假设哈希函数无冲突且计数器永不溢出：
% \begin{itemize}
%     \item \textbf{插入}：每个元素 \( x \) 的 \( k \) 个计数器递增，计数严格反映插入次数。
%     \item \textbf{删除}：每个计数器递减，计数归零时元素被正确移除。
%     \item \textbf{查询}：仅当所有计数器非零时返回存在，无假阴性，假阳性率为 0。
% \end{itemize}

% \subsubsection*{实际场景下的误差控制}
% 在哈希冲突和计数器溢出时：
% 1. \textbf{假阳性率}：  
%     当 \( x \notin S \) 但所有 \( C[h_j(x)] > 0 \)，概率为：
%     \[
%     \Pr[\text{False Positive}] \leq \left( 1 - \left(1 - \frac{1}{m}\right)^{kn} \right)^k \approx \left(1 - e^{-kn/m}\right)^k.
%     \]
%     通过选择 \( m = \frac{kn}{\ln 2} \)，可将假阳性率降至 \( 2^{-k} \)。

% 2. \textbf{溢出处理正确性}：  
%     溢出计数器通过外部哈希表记录精确计数，确保删除操作不会因截断导致计数错误。

% \subsection*{复杂度分析}
% \subsubsection*{空间复杂度}
% \begin{itemize}
%     \item \textbf{主计数器数组}：\( m \) 个计数器，每个占 \( b \) 位，总空间为 \( O(mb) \)。
%     \item \textbf{外部哈希表}：仅存储溢出元素，假设溢出比例为 \( p \)，空间为 \( O(pn \log n) \)。  
%     通过选择 \( b = 4 \)，溢出概率 \( p \approx \frac{n}{m} \cdot 2^{-b} \)，总空间为：
%     \[
%     O\left( \frac{kn}{\ln 2} \cdot 4 + \frac{n^2}{m} \cdot \log n \right) = O\left( kn + \frac{n \log n}{k} \right).
%     \]
%     当 \( k = \Theta(\log n) \)，空间复杂度为 \( O(n \log n) \)。
% \end{itemize}

% \subsubsection*{时间复杂度}
% \begin{itemize}
%     \item \textbf{插入/删除}：\( O(k) \) 次哈希和计数器操作，溢出时额外 \( O(1) \) 哈希表操作。
%     \item \textbf{查询}：\( O(k) \) 次哈希检查，溢出时额外 \( O(1) \) 哈希表查询。
% \end{itemize}

% \subsubsection*{参数优化}
% 选择 \( k = \log_2 \frac{1}{\delta} \) 和 \( m = \frac{kn}{\ln 2} \)，可满足：
% \[
% \text{假阳性率} \leq \delta, \quad \text{空间} = O\left( n \log \frac{1}{\delta} \right).
% \]

% \subsection*{性能对比}
% \begin{table}[h]
% \centering
% \begin{tabular}{|l|c|c|}
% \toprule
% \textbf{指标} & \textbf{传统 CBF} & \textbf{动态 CBF} \\ 
% \midrule
% 空间 & \( O(mb) \) & \( O(n \log n) \) \\ 
% 假阳性率 & \( (1 - e^{-kn/m})^k \) & \( \delta \)（可控） \\ 
% 删除支持 & 是（但易溢出） & 是（精确处理溢出） \\ 
% \bottomrule
% \end{tabular}
% \end{table}

% \subsection*{结论}
% 动态可调计数器的 Filter 在支持删除操作时，通过以下优化显著提升性能：
% 1. \textbf{精确溢出处理}：外部哈希表记录溢出计数，避免传统 CBF 的截断错误。
% 2. \textbf{空间效率}：通过动态分配，减少固定位数计数器的空间浪费。
% 3. \textbf{可控误差}：参数 \( k \) 和 \( m \) 可灵活调节假阳性率至任意 \( \delta > 0 \)。


% \section{Muximum Load}

% {\color{gray}
% 将 power of two choices 扩展到 power of $d$ choices。此时 maximum load 是多少？试分析和证明你的结论。
% }

% \textbf{解：} \\

% \subsection*{问题定义与符号约定}
% \begin{itemize}
%     \item 将 \(n\) 个球投入 \(n\) 个桶中，每次随机选择 \(d\) 个桶，并将球放入当前负载最小的桶。
%     \item 定义最大负载 \(L = \max_{1 \leq i \leq n} X_i\)，其中 \(X_i\) 为第 \(i\) 个桶的负载。
%     \item 定义 \(\beta_i\) 为负载至少为 \(i\) 的桶的数量。
% \end{itemize}

% \subsection*{核心递推关系}
% 通过分层归纳法分析 \(\beta_i\) 的衰减行为：
% \begin{enumerate}
%     \item \textbf{初始条件}：\(\beta_0 = n\)（所有桶初始为空）。
%     \item \textbf{递推假设}：假设 \(\beta_i \leq C \cdot \frac{\beta_{i-1}^d}{n^{d-1}}\)，其中 \(C\) 为常数。
%     \item \textbf{概率分析}：当存在 \(\beta_{i}\) 个负载 \(\geq i\) 的桶时，新球选择 \(d\) 个桶均属于这 \(\beta_i\) 个桶的概率为：
%         \[
%         \Pr[\text{新球导致负载增至 } i+1] \leq \left( \frac{\beta_i}{n} \right)^d.
%         \]
%         因此，期望有：
%         \[
%         \mathbb{E}[\beta_{i+1}] \leq n \cdot \left( \frac{\beta_i}{n} \right)^d = \frac{\beta_i^d}{n^{d-1}}.
%         \]
% \end{enumerate}

% \subsection*{递推求解}
% 对递推式 \(\beta_{i+1} = \frac{\beta_i^d}{n^{d-1}}\) 取对数：
% \[
% \log \beta_{i+1} = d \log \beta_i - (d-1) \log n.
% \]
% 递推展开后可得：
% \[
% \log \beta_i = d^i \log \beta_0 - (d^{i} - 1) \log n = d^i \log n - (d^i - 1) \log n = \log n.
% \]
% 此矛盾表明递推需在有限步终止。实际分析中，递推终止条件为 \(\beta_i \leq 1\)，解得最大负载：
% \[
% L = O\left( \frac{\log \log n}{\log d} \right).
% \]

% \subsection*{严格概率控制}
% \begin{enumerate}
%     \item \textbf{Chernoff 边界}：设 \(\nu_i\) 为负载 \(\geq i\) 的桶数，对任意 \(t > 0\)：
%         \[
%         \Pr[\nu_{i+1} \geq t \cdot \mathbb{E}[\nu_{i+1}]] \leq e^{-t \cdot \mathbb{E}[\nu_{i+1}]}.
%         \]
%     \item \textbf{联合概率界}：通过分层递推，总误差概率为：
%         \[
%         \Pr\left[\exists i, \nu_i > \beta_i\right] \leq \sum_{i=1}^L e^{-\Omega(\beta_i)} = o(1).
%         \]
%         当 \(\beta_i\) 按 \(d\) 指数衰减时，\(L = O\left( \frac{\log \log n}{\log d} \right)\) 以高概率成立。
% \end{enumerate}

% \subsection*{结论}
% 对于 Power of \(d\) Choices 策略：
% \begin{itemize}
%     \item 当 \(n\) 个球投入 \(n\) 个桶时，最大负载为：
%         \[
%         L = O\left( \frac{\log \log n}{\log d} \right) \quad \text{以高概率}.
%         \]
%     \item 当球数 \(m \geq n \log n\) 时，最大负载退化为 \(O\left( \frac{m}{n} \right)\)。
% \end{itemize}

% \subsection*{证明总结}
% \begin{itemize}
%     \item \textbf{递推分析}：通过 \(\beta_i\) 的指数衰减刻画负载增长。
%     \item \textbf{概率工具}：利用 Chernoff 边界控制误差传播。
%     \item \textbf{参数优化}：\(d\) 的增加加速负载均衡，但收益随 \(\log d\) 递减。
% \end{itemize}

% \textbf{想法：} 通过这道题的计算，可以得知增加选择数 \(d\) 可显著降低最大负载，有助于分布式系统设计

% \section{Cuckoo Hashing}

% {\color{gray}
% 我们在对cuckoo hashing的分析中假设了我们的哈希函数是真随机的。如果它是 pairwise independent，会有什么问题？尝试计算这些问题出现的概率。
% }

% \textbf{解：} \\

% \subsection*{Pairwise Independent 哈希函数的问题}
% 当哈希函数仅为 \textbf{pairwise independent} 时，Cuckoo Hashing 的性能可能退化，主要问题如下：

% \subsubsection*{1. 长路径与循环的概率增加}
% 真随机哈希函数下，插入过程中路径长度 \( k \) 的概率为 \( \Pr[\text{len(path)} \geq k] \leq \exp(-\Omega(k)) \)。  
% 但在 pairwise independent 哈希函数下，由于缺乏高阶独立性，路径长度的概率衰减可能更慢。具体地，对固定路径 \( x_1 \to x_2 \to \ldots \to x_k \)，其存在概率上界为：
% \[
% \Pr[\text{路径存在}] \leq \frac{n^k}{m^{k+1}},
% \]
% 而当 \( m = O(n) \) 时，此概率为 \( \Omega\left( \frac{1}{n} \right) \)，远高于真随机情况下的指数衰减。

% \subsubsection*{2. 插入失败概率的上升}
% 真随机哈希函数下，插入失败（出现不可解析的循环或长路径）的概率为 \( O(1/n) \)。  
% 但在 pairwise independent 哈希函数下，由于循环结构的统计依赖性增强，失败概率可能上升至：
% \[
% \Pr[\text{插入失败}] \leq \sum_{k \geq 2} \frac{n^k}{m^{2k}} = O\left( \frac{n^2}{m^4} \right),
% \]
% 当 \( m = \Theta(n) \) 时，此概率为 \( \Omega(1/n^2) \)，显著高于真随机情况。

% \subsection*{严格概率计算}
% \subsubsection*{长路径概率}
% 设哈希表大小为 \( m = cn \)（常数 \( c > 1 \)），路径长度 \( k \) 的生成需满足：
% \begin{align*}
% \Pr[\text{len(path)} \geq k] &\leq \sum_{\text{所有可能路径}} \Pr[\text{路径存在}] \\
% &\leq n^{k} \cdot \left( \frac{1}{m} \right)^{k} \quad (\text{pairwise independence 下每步选择概率界}) \\
% &= \left( \frac{n}{m} \right)^k = \left( \frac{1}{c} \right)^k.
% \end{align*}
% 当 \( k = \Theta(\log n) \) 时，概率为 \( n^{-\Theta(1)} \)，但仍高于真随机的指数衰减 \( \exp(-\Omega(\log n)) = n^{-\Omega(1)} \)。

% \subsubsection*{循环生成概率}
% 对于长度为 \( k \) 的循环，其存在概率为：
% \[
% \Pr[\text{存在 } k\text{-循环}] \leq \binom{n}{k} \cdot \left( \frac{1}{m} \right)^{2k} = O\left( \frac{n^k}{m^{2k}} \right).
% \]
% 当 \( m = \Theta(n) \) 时，概率为 \( O\left( \frac{1}{n^k} \right) \)，但对 \( k = 2 \) 的情况，概率为 \( \Omega(1/n^2) \)，显著高于真随机下的 \( O(1/n^4) \)。

% \subsection*{结论}
% 当哈希函数仅为 pairwise independent 时：
% \begin{itemize}
%     \item 插入失败概率从 \( O(1/n) \) 上升至 \( \Omega(1/n^2) \)。
%     \item 期望插入时间可能从 \( O(1) \) 退化为 \( O(\log n) \)。
%     \item 长路径与循环的概率不再被指数压制，导致重建频率增加。
% \end{itemize}
% \textbf{想法：}这些问题的根源在于 pairwise independence 无法保证哈希值的高阶独立性，从而破坏了 Cuckoo Hashing 依赖的随机性假设。

% \section{Feistel Cipher}

% {\color{gray}
% Feistel cipher / Feistel permutation 是一种对称、按如下方式运行的加密方式：
%   $$(x_1, x_2) \mapsto (f(x_1) \oplus x_2, x_2)$$
%   若假设所有的 $x_2$ 都不相同，且哈希函数 $h$ 是 $k$-universal，则 Feistel cipher 可以把 $x_1 \in \{0,1\}^t$ 以 $k$-universal 的方式均匀随机地映射到 $\{0,1\}^t$ 中。
  
%   请构造一个一般的 Feistel cipher，若所有的 $x_2$ 都不相同，则新的 Feistel cipher 将 $x_1 \in [n]$ 以 $k$-universal 的方式均匀随机地映射到 $[n]$ 中，并证明它是 $k$-universal。
% }

% \textbf{解：} \\

% \subsection*{一般 Feistel Cipher 构造}
% 设输入域为 \( [n] \)，将其分解为两部分 \( x_1 \in [n_1] \) 和 \( x_2 \in [n_2] \)，满足 \( n_1 \times n_2 = n \)。构造如下：
% \begin{enumerate}
%     \item \textbf{输入分解}：对任意输入 \( x \in [n] \)，唯一表示为 \( x = x_1 \cdot n_2 + x_2 \)，其中 \( x_1 \in [n_1] \), \( x_2 \in [n_2] \)，且所有 \( x_2 \) 互不相同。
%     \item \textbf{哈希函数选择}：选取一个 \( k\)-universal 哈希函数族 \( \mathcal{H} \)，其中每个 \( h \in \mathcal{H} \) 满足：
%     \[
%     h: [n_1] \rightarrow [n_2].
%     \]
%     \item \textbf{Feistel 操作}：定义双射 \( F_h: [n] \rightarrow [n] \) 为：
%     \[
%     F_h(x) = \left( \left( h(x_1) + x_2 \right) \mod n_2 \right) \cdot n_1 + x_1,
%     \]
%     其中 \( + \) 和 \( \mod \) 运算在 \( [n_2] \) 上定义。
% \end{enumerate}

% \subsection*{\(k\)-Universal 性质证明}
% 需证明：对任意 \( k \) 个不同输入 \( x^{(1)}, x^{(2)}, \ldots, x^{(k)} \in [n] \)，以及任意 \( k \) 个输出 \( y^{(1)}, y^{(2)}, \ldots, y^{(k)} \in [n] \)，有：
% \[
% \Pr_{h \in \mathcal{H}} \left[ \bigwedge_{i=1}^k F_h(x^{(i)}) = y^{(i)}} \right] = \frac{1}{n^k}.
% \]

% \subsubsection*{步骤 1：输入输出关系分析}
% 设 \( x^{(i)} = x_1^{(i)} \cdot n_2 + x_2^{(i)} \)，\( y^{(i)} = y_1^{(i)} \cdot n_1 + y_2^{(i)} \)，其中 \( y_1^{(i)} \in [n_2] \), \( y_2^{(i)} \in [n_1] \)。  
% 根据 Feistel 操作定义，方程 \( F_h(x^{(i)}) = y^{(i)} \) 等价于：
% \[
% \begin{cases}
%     h(x_1^{(i)}) + x_2^{(i)} \equiv y_1^{(i)} \mod n_2, \\
%     x_1^{(i)} = y_2^{(i)}.
% \end{cases}
% \]

% \subsubsection*{步骤 2：约束条件化简}
% 由于 \( x_1^{(i)} = y_2^{(i)} \)，第二个方程直接确定 \( y_2^{(i)} \)。  
% 第一个方程可写为：
% \[
% h(y_2^{(i)}) \equiv y_1^{(i)} - x_2^{(i)} \mod n_2 \quad (\forall 1 \leq i \leq k).
% \]
% 由于所有 \( x_2^{(i)} \) 互不相同，且 \( y_2^{(i)} \) 由输入唯一确定，需满足 \( y_2^{(i)} = x_1^{(i)} \)。

% \subsubsection*{步骤 3：哈希函数的 \(k\)-Universal 性质}
% 根据 \( \mathcal{H} \) 的 \(k\)-universal 定义，对任意 \( k \) 个不同的 \( y_2^{(1)}, y_2^{(2)}, \ldots, y_2^{(k)} \in [n_1] \)，有：
% \[
% \Pr_{h \in \mathcal{H}} \left[ \bigwedge_{i=1}^k h(y_2^{(i)}) = \left( y_1^{(i)} - x_2^{(i)} \right) \mod n_2 \right] = \frac{1}{n_2^k}.
% \]
% 由于 \( y_2^{(i)} \) 由输入唯一确定且互不相同，且每个 \( y_1^{(i)} \) 在 \( [n_2] \) 上独立均匀分布，总概率为：
% \[
% \Pr = \frac{1}{n_2^k} \cdot \frac{1}{n_1^k} = \frac{1}{(n_1 n_2)^k} = \frac{1}{n^k}.
% \]

% \subsection*{结论}
% 构造的 Feistel cipher \( F_h \) 满足 \(k\)-universal 性质，即对任意 \( k \) 个不同输入和输出，其映射概率为 \( 1/n^k \)。此结果依赖于：
% 1. 输入分解的唯一性（所有 \( x_2 \) 不同），
% 2. 哈希函数族 \( \mathcal{H} \) 的 \(k\)-universal 性质，
% 3. Feistel 结构的双射性保证输出覆盖整个空间 \( [n] \)。

% \textbf{想法：}该构造可广泛应用于需要高效随机映射的密码学协议中。

\section{Simple Tabulation Hashing 扩展}

{\color{gray}
课程中介绍的 tabulation hashing 被称为 simple tabulation hashing。 tabulation hashing 还有不少别的扩展和强化。尝试调查并介绍其中一些，并尝试解释该 tabulation hashing 克服了其他 tabulation hashing 的什么问题，有什么优点和缺点，尝试解释为什么。你也可以调查一个你喜欢的哈希函数。你的分数取决于你的解释的详细程度和准确程度。
}

\textbf{解：} \\


\section{Johnson-Lindenstrauss 变换与 $\ell_\infty$ 降维}

{\color{gray}
考虑使用 Johnson-Lindenstrauss Transformation 对某个单位向量进行降维。如果我们保证该单位向量具有较低的 $\ell_\infty$-norm，即对于单位向量 $\mathbf{x} \in \mathbb{S}^{d-1}$ 保证 $\max_i \mathbf{x}_i$ 较低，我们可以在保证正确性的前提下降到更低的维数吗？可以降低到多少维？你的答案应该包含 $d, \epsilon, \delta, \ell_\infty$ 四个参数。尝试证明你的结论。
}

\textbf{解：} \\
考虑单位向量 $\mathbf{x} \in \mathbb{S}^{d-1}$，满足 $\|\mathbf{x}\|_\infty = \max_i |x_i| \leq M$，其中 $M$ 较小。

设标准高斯 JLT 投影矩阵 $A \in \mathbb{R}^{k \times d}$ 的元素服从独立同分布 $\mathcal{N}(0, 1/k)$，目标是在保证：
\[
(1 - \epsilon) \leq \|A\mathbf{x}\|_2^2 \leq (1 + \epsilon)
\]
以概率至少 $1 - \delta$ 成立的条件下，最小化降维维度 $k$。

\textbf{结论：} 即使 $\|\mathbf{x}\|_\infty$ 很小，也不能降低 $k$。即：
\[
k = \Theta\left(\frac{\log(1/\delta)}{\epsilon^2}\right)
\]
是 \textbf{紧的下界}，且与 $\|\mathbf{x}\|_\infty$ 无关。

\textbf{理由：}
\begin{itemize}
  \item 每一行 $(A\mathbf{x})_i = \sum_{j=1}^d A_{ij} x_j$ 是一组独立高斯变量的线性组合。
  \item 由于 $\|\mathbf{x}\|_2 = 1$，则 $(A\mathbf{x})_i \sim \mathcal{N}(0, 1/k)$，无论 $\mathbf{x}$ 的 $\ell_\infty$ 有多小。
  \item 故 $\|A\mathbf{x}\|_2^2 = \sum_{i=1}^k Y_i^2$，其中 $Y_i \sim \mathcal{N}(0, 1/k)$ 独立同分布，分布与 $\mathbf{x}$ 无关。
\end{itemize}

\textbf{使用集中不等式：}
\[
\Pr\left[ \left| \|A\mathbf{x}\|_2^2 - 1 \right| > \epsilon \right] \leq 2\exp\left(-\frac{k\epsilon^2}{8}\right)
\Rightarrow
k \geq \frac{8}{\epsilon^2} \log\left(\frac{2}{\delta}\right)
\]

\textbf{反例说明：} 即使设 $\|\mathbf{x}\|_\infty = \frac{1}{\sqrt{d}}$（如均匀分布向量），投影后 $\|A\mathbf{x}\|_2^2$ 的分布与 $\mathbf{x} = \mathbf{e}_1$ 相同，无法提高集中性。

\textbf{结论：}
\[
\boxed{k = \Theta\left(\frac{\log(1/\delta)}{\epsilon^2}\right)}
\]
其中常数与 $\ell_\infty$-范数无关。

\textbf{备注：} 若使用稀疏 JLT（如 Count Sketch），低 $\ell_\infty$ 向量可能降低误差，但该方法不满足标准 JLT 的高概率误差界，故不适用于本题的单向量精度保证。

\vspace{8em}

\section{LSH 的集合视角与构造}

{\color{gray}
LSH 函数的设计宗旨是尽量使得相邻的点容易碰撞的同时，相距较远的点不容易碰撞。我们可以反过来想象“被放入某一个桶中的数据点的集合”。这个集合是随机的，在课程中介绍的LSH中，这样的集合是一个随机的Hamming cube。从这个角度考虑的话，不严格地说，我们可以想象，“如果一个数据点被放入了这样一个随机的集合中，当这样的集合是什么形状的时候，相邻的数据点比较容易也在这个集合中，而较远的数据点不容易在这个集合中”。
}

\subsection*{(a) Metric Space on $([0,n]^d, \mathrm{d})$}

{\color{gray}
考虑 metric space $([0,n]^d,\mathrm{d})$，$\mathrm{d}(x,y)=\sqrt{\sum_i (\min\{|x_i-y_i|,n-|x_i-y_i|\})^2}$。（想象在每个维度上都“绕回”，即 $(0,0,0,\dots)$ 和 $(n,n,n,\dots)$ 的距离是 $0$。）对于这样的 metric space 你会如何设计 LSH 函数？尝试解释为什么。
}

\textbf{解：} \\
\subsubsection*{度量定义}

\[
\mathrm{d}(x,y) = \sqrt{\sum_{i=1}^d \left( \min\left\{ |x_i - y_i|,  n - |x_i - y_i| \right\} \right)^2}
\]

该度量在每一维上具有周期性（环形结构），即坐标差在模 \(n\) 意义下计算最小距离。

\subsubsection*{LSH 设计：分治投影哈希}

\textbf{核心思想}：利用环形结构的局部欧几里得特性，将高维环面分解为独立的一维环，每维使用模线性哈希，再通过 AND-OR 结构组合。

\textbf{步骤}:

\begin{enumerate}[label=\arabic*.]
    \item \textbf{单维哈希函数}：

    对第$i$维，定义哈希函数：
    
    \[
    h_i^{(b_i)}(x_i) = \left\lfloor \frac{x_i + b_i \bmod n}{w} \right\rfloor
    \]
    其中 \(b_i \sim \text{Uniform}[0, w)\)，\(w\) 为桶宽。
    
    \item \textbf{高维组合函数}：

    定义整体哈希函数为：
    
    \[
    h_{\mathbf{b}}(x) = \left( h_1^{(b_1)}(x_1),  h_2^{(b_2)}(x_2),  \dots,  h_d^{(b_d)}(x_d) \right)
    \]

    \item \textbf{AND-OR 放大结构}：
    \begin{itemize}
        \item AND: \(k\) 个独立函数，需全部碰撞
        \item OR: \(L\) 个 AND 组，只需任一组碰撞
    \end{itemize}
\end{enumerate}

\subsection*{碰撞概率分析}

\textbf{单维碰撞概率}：记 \(\delta_i = \min\{|x_i - y_i|, n - |x_i - y_i|\}\)，则：
\[
\Pr\left[ h_i^{(b_i)}(x_i) = h_i^{(b_i)}(y_i) \right] = \max\left\{ 0,  1 - \frac{\delta_i}{w} \right\}
\]

\textbf{高维碰撞概率}：

\begin{align*}
p_{\text{AND}} &= \prod_{i=1}^d \max\left\{ 0,  1 - \frac{\delta_i}{w} \right\} \\
p &= 1 - \left(1 - p_{\text{AND}}\right)^L
\end{align*}

\textbf{邻近点（\(\mathrm{d}(x,y) \leq r\)）}：设 \(w = \alpha r\)，则
\[
p_{\text{AND}} \geq \left(1 - \frac{1}{\alpha} \right)^d
\]

\textbf{远距点（\(\mathrm{d}(x,y) > cr\)）}：存在 \(\delta_j > \frac{cr}{\sqrt{d}}\)，则：
\[
p_{\text{AND}} \leq 1 - \frac{c}{\alpha \sqrt{d}}
\]

通过选择合适的 \(k, L\)，可实现 LSH 条件：\(p_1 \gg p_2\)。

\subsubsection*{参数优化}

\begin{itemize}
    \item 桶宽：\(w = \Theta\left(\frac{r}{\sqrt{d}}\right)\)
    \item AND 数：\(k = \Theta(1)\)
    \item OR 数：\(L = \Theta\left( \frac{\log(1/p_2)}{p_{\text{AND}}} \right)\)
    \item 最终性能：\(\rho = \frac{\log p_1}{\log p_2} \approx \frac{1}{c}\)
\end{itemize}

\subsubsection*{(b) Sphere Metric Space $(\mathbb{S}^{d-1}, \mathrm{d})$}

{\color{gray}
再考虑球面空间 $(\mathbb{S}^{d-1},\mathrm{d})$，其中 $\mathrm{d}(x,y)=\arccos\langle x,y\rangle$（即所有点都是单位向量，我们用两个向量的夹角角度表示向量之间的距离）。对于这样的 metric space 你会如何设计 LSH 函数？尝试解释为什么。
}

\textbf{解：} \\
\subsubsection*{度量定义}

\[
\mathrm{d}(x,y) = \arccos(\langle x, y \rangle)
\]

即单位向量间夹角。

\subsubsection*{LSH 设计：超平面分割（Hyperplane LSH）}

\begin{enumerate}[label=\arabic*.]
    \item 采样随机单位向量 \(\mathbf{a} \sim \text{Uniform}(\mathbb{S}^{d-1})\)
    \item 定义哈希函数：
    \[
    h_{\mathbf{a}}(x) = \text{sign}(\langle \mathbf{a}, x \rangle)
    \]
    \item 采用 AND-OR 放大结构与前一致
\end{enumerate}

\subsubsection*{碰撞概率分析}

设 \(x,y \in \mathbb{S}^{d-1}\)，夹角 \(\theta = \mathrm{d}(x,y)\)，则：
\[
\Pr\left[ h_{\mathbf{a}}(x) = h_{\mathbf{a}}(y) \right] = 1 - \frac{\theta}{\pi}
\]

\textbf{邻近点（\(\theta \leq r\)）}：
\[
p_1 = 1 - \frac{r}{\pi}
\]

\textbf{远距点（\(\theta > cr\)）}：
\[
p_2 = 1 - \frac{cr}{\pi}
\]

最终性能：
\[
\rho = \frac{\log p_1}{\log p_2} \approx \frac{1}{c} \quad (\text{当 } r \ll \pi)
\]

\subsubsection*{最优性说明}

\begin{itemize}
    \item 碰撞概率线性：\(1 - \theta / \pi\)
    \item 任意球面 LSH 的下界：\(\rho \geq \frac{\log(1 - r/\pi)}{\log(1 - cr/\pi)} \approx \frac{1}{c}\)
    \item 超平面哈希达到该下界，故为最优
\end{itemize}

\section*{总结}

\begin{center}
\begin{tabular}{|c|c|c|c|}
\hline
\textbf{空间类型} & \textbf{LSH 设计} & \textbf{关键参数} & \(\rho\) 值 \\
\hline
环面空间 & 模线性哈希 + AND-OR & \(w = \Theta(r / \sqrt{d})\) & \(\approx 1/c\) \\
球面空间 & 超平面 LSH + AND-OR & 无额外参数 & \(= 1/c\) \\
\hline
\end{tabular}
\end{center}

\textbf{几何启发}：
\begin{itemize}
    \item \textbf{环面空间}：局部欧几里得性 + 周期性，分维处理
    \item \textbf{球面空间}：旋转不变性，允许全局分割
\end{itemize}

\section{OSE 维度下界证明}

{\color{gray}
假设 $n\gg d$（不妨假设 $n > 2^d$）。考虑使用一个 column sparsity 只有 $1$ 的矩阵 $\Pi \in \mathbb{R}^{m\times n}$ 进行 Oblivious Subspace Embedding (OSE)。考虑这样的\textbf{随机}矩阵 $A \in \{0,1\}^{n\times d}$：$A$ 的每一列随机选择一个位置为 $1$，其他位置为 $0$。尝试证明，任意\textbf{确定}的 $\Pi$ 为$A$的列空间进行子空间嵌入都至少需要 $m=\Omega(d^2)$，即它作为 OSE 不可能降维到 $o(d^2)$。提示：尝试观察 $\Pi A e_i, \Pi A(e_i+e_j)$ 的 $\ell_2$-norm，以及你可能需要应用 birthday paradox（随机地把 $d$ 个球丢入 $m$ 个桶中，那么要满足 $m=\Omega(d^2)$ 才能使得有 $\Omega(1)$ 的概率，这 $d$ 个球中不会有两个球被丢进了同一个桶中）。
}

\textbf{解：} \\


\section{点容量最大流建模}

{\color{gray}
点容量问题：现在我们的网络流问题中，每条边的容量是无穷大，但是流经每个节点 $i$ 有容量限制 $c_i$。设计一个求解该模型的最大流的算法，尝试解释其正确性。
}

\textbf{解：} \\


\section{采购优化问题建模}

{\color{gray}
某工厂生产产品需 $k$ 种零件。供应商有 $m$ 家，第 $i\in[m]$ 家提供零件 $j\in[k]$ 的价格为 $a_{ij}$，且最多供应 $c_{ij}$ 个。工厂每天需零件 $j\in[k]$ 的数量为 $d_j$。设计一个算法求解最小化总采购成本，尝试解释其正确性。
}

\textbf{解：} \\


\section{二分图匹配与顶点覆盖对偶性}

{\color{gray}
令 $G = (L, R, E)$ 为一个二分图。写出该图最大匹配的线性规划形式，以及最小顶点覆盖的线性规划形式。证明该图中的最大匹配大小等于最小顶点覆盖的大小。这表明，在二分图中，顶点覆盖问题是可以在多项式时间内求解的。
}

\textbf{解：} \\


\section{0-1 背包问题的线性松弛与近似算法}

{\color{gray}
考虑 0-1 背包问题：有 $n$ 个物品，第 $i$ 个物品的价值和重量分别是 $v_i,w_i$，背包容量 $C$。请写出对应的整数规划，和它的线性规划松弛。设计一个根据松弛线性规划最优解取整以取得优秀整数可行解的方案，并尝试分析你的答案与最优解的近似比。
}

\textbf{解：} \\

\end{document}
